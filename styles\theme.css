/* 
 * Isopur Dark Theme
 * Modern dark-leaning theme with macOS-style aesthetics
 * Based on design system guidelines from README.md
 */

/* Import Inter Variable font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap');

/* Import AOS (Animate On Scroll) library */
@import url('https://unpkg.com/aos@2.3.1/dist/aos.css');

/* CSS Custom Properties (Design Tokens) */
:root {
  /* Color Palette */
  --clr-bg: #1a1a1c;                    /* overall canvas (≈ macOS dark) */
  --clr-surface: #252529;               /* cards, nav, footer */
  --clr-accent: #ff4040;                /* inherited from Isomat red; use sparingly for CTAs */
  --clr-text-primary: #e6e6e6;         /* body copy */
  --clr-text-muted: #9a9a9a;           /* meta, captions */
  
  /* Additional semantic colors */
  --clr-surface-elevated: #2d2d31;     /* elevated cards, modals */
  --clr-border: #3a3a3e;               /* subtle borders */
  --clr-border-light: #4a4a4e;         /* lighter borders for hover states */
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Glass morphism */
  --glass-bg: rgba(37, 37, 41, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-blur: blur(12px);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  background-color: var(--clr-bg);
  color: var(--clr-text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography overrides */
h1, h2, h3, h4, h5, h6 {
  color: var(--clr-text-primary);
  font-weight: var(--font-weight-semibold);
}

p {
  color: var(--clr-text-primary);
}

.text-muted {
  color: var(--clr-text-muted) !important;
}

/* Surface elements */
.bg-light {
  background-color: var(--clr-surface) !important;
}

.bg-gray {
  background-color: var(--clr-surface) !important;
}

.bg-dark {
  background-color: var(--clr-bg) !important;
}

.bg-soft-primary {
  background-color: var(--clr-surface-elevated) !important;
}

/* Cards */
.card {
  background-color: var(--clr-surface);
  border: 1px solid var(--clr-border);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.card:hover {
  border-color: var(--clr-border-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-body {
  color: var(--clr-text-primary);
}

/* Glass effect for navigation */
.navbar {
  background-color: var(--glass-bg) !important;
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-bottom: 1px solid var(--glass-border);
  transition: all var(--transition-normal);
}

.navbar.navbar-light .navbar-nav .nav-link {
  color: var(--clr-text-primary);
  transition: color var(--transition-fast);
}

.navbar.navbar-light .navbar-nav .nav-link:hover,
.navbar.navbar-light .navbar-nav .nav-link.active {
  color: var(--clr-accent);
}

/* Buttons */
.btn-primary {
  background-color: var(--clr-accent);
  border-color: var(--clr-accent);
  color: white;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.btn-primary:hover {
  background-color: #e63636;
  border-color: #e63636;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Footer */
footer {
  background-color: var(--clr-bg) !important;
  border-top: 1px solid var(--clr-border);
}

footer .widget-title {
  color: var(--clr-text-primary) !important;
}

footer a {
  color: var(--clr-text-muted);
  transition: color var(--transition-fast);
}

footer a:hover {
  color: var(--clr-accent);
}

/* Forms */
.form-control {
  background-color: var(--clr-surface);
  border: 1px solid var(--clr-border);
  color: var(--clr-text-primary);
  transition: all var(--transition-fast);
}

.form-control:focus {
  background-color: var(--clr-surface-elevated);
  border-color: var(--clr-accent);
  box-shadow: 0 0 0 0.2rem rgba(255, 64, 64, 0.25);
  color: var(--clr-text-primary);
}

.form-control::placeholder {
  color: var(--clr-text-muted);
}

/* Links */
a {
  color: var(--clr-accent);
  transition: color var(--transition-fast);
}

a:hover {
  color: #e63636;
}

.link-dark {
  color: var(--clr-text-primary) !important;
}

.link-dark:hover {
  color: var(--clr-accent) !important;
}

/* Accessibility: Respect prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .navbar {
    background-color: var(--clr-surface) !important;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--clr-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--clr-border);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--clr-border-light);
}

/* Selection styles */
::selection {
  background-color: var(--clr-accent);
  color: white;
}

::-moz-selection {
  background-color: var(--clr-accent);
  color: white;
}

/* AOS Animation Customizations */
[data-aos] {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom AOS animations for dark theme */
[data-aos="fade-up"] {
  transform: translate3d(0, 40px, 0);
  opacity: 0;
}

[data-aos="fade-up"].aos-animate {
  transform: translate3d(0, 0, 0);
  opacity: 1;
}

[data-aos="fade-in"] {
  opacity: 0;
}

[data-aos="fade-in"].aos-animate {
  opacity: 1;
}

/* CSS Scroll-Timeline Animations (Modern browsers) */
@supports (animation-timeline: scroll()) {
  .parallax-hero {
    animation: parallax-move linear;
    animation-timeline: scroll();
    animation-range: 0% 100%;
  }

  @keyframes parallax-move {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(-20%);
    }
  }

  .fade-in-scroll {
    animation: fade-in-timeline linear;
    animation-timeline: scroll();
    animation-range: entry 0% entry 50%;
  }

  @keyframes fade-in-timeline {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* Fallback for browsers without scroll-timeline support */
@supports not (animation-timeline: scroll()) {
  .parallax-hero {
    transform: translateY(0);
    transition: transform 0.1s ease-out;
  }

  .fade-in-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
  }

  .fade-in-scroll.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced card hover animations */
.card {
  transition: all var(--transition-normal), transform var(--transition-fast);
}

.card:hover {
  transform: translateY(-8px) scale(1.02);
}

/* Button hover enhancements */
.btn {
  transition: all var(--transition-fast);
}

.btn:hover {
  transform: translateY(-2px);
}

/* Navigation scroll effect */
.navbar.scrolled {
  background-color: var(--clr-surface) !important;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  box-shadow: var(--shadow-md);
}

/* Loading animation for images */
img {
  transition: opacity var(--transition-normal);
}

img[loading="lazy"] {
  opacity: 0;
}

img[loading="lazy"].loaded {
  opacity: 1;
}
