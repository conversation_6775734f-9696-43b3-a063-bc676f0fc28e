@import url(https://fonts.googleapis.com/css2?family=IBM+Plex+Serif:ital,wght@1,300;1,400;1,500;1,600;1,700);
@font-face {
  font-family: 'Space Grotesk';
  src: url(../../fonts/space/SpaceGrotesk-SemiBold.woff2) format('woff2'), url(../../fonts/space/SpaceGrotesk-SemiBold.woff) format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: block
}
@font-face {
  font-family: 'Space Grotesk';
  src: url(../../fonts/space/SpaceGrotesk-Light.woff2) format('woff2'), url(../../fonts/space/SpaceGrotesk-Light.woff) format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: block
}
@font-face {
  font-family: 'Space Grotesk';
  src: url(../../fonts/space/SpaceGrotesk-Bold.woff2) format('woff2'), url(../../fonts/space/SpaceGrotesk-Bold.woff) format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: block
}
@font-face {
  font-family: 'Space Grotesk';
  src: url(../../fonts/space/SpaceGrotesk-Medium.woff2) format('woff2'), url(../../fonts/space/SpaceGrotesk-Medium.woff) format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: block
}
@font-face {
  font-family: 'Space Grotesk';
  src: url(../../fonts/space/SpaceGrotesk-Regular.woff2) format('woff2'), url(../../fonts/space/SpaceGrotesk-Regular.woff) format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: block
}
* {
  word-spacing: normal !important
}
body {
  font-family: "Space Grotesk", sans-serif;
  font-size: .85rem
}
em {
  font-family: "IBM Plex Serif", serif
}
.counter-wrapper p,
.lead,
.nav-tabs.nav-tabs-bg .nav-link p,
blockquote,
body {
  font-weight: 400
}
.accordion-wrapper .card-header button,
.badge,
.btn,
.btn.btn-circle .number,
.collapse-link,
.display-1,
.display-2,
.display-3,
.display-4,
.display-5,
.display-6,
.dropdown-item,
.filter:not(.basic-filter),
.filter:not(.basic-filter) ul li a,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
.meta,
.more,
.nav-link,
.post-category,
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600
}
.btn .more,
.dropdown-item,
.nav-link {
  letter-spacing: normal
}
.btn,
.lg-sub-html p,
.nav-link,
.nav-link p,
.navbar .btn-sm {
  font-size: .85rem
}
.dropdown-menu {
  font-size: .8rem
}
.btn-group-sm>.btn,
.btn-sm,
.post-meta,
.share-dropdown .dropdown-menu .dropdown-item {
  font-size: .75rem
}
.accordion-wrapper .card-header button,
.collapse-link,
.nav-tabs .nav-link {
  font-size: .9rem !important
}
.btn {
  padding-top: .55rem;
  padding-bottom: .45rem
}
.btn-group-sm>.btn,
.btn-sm {
  padding-top: .45rem;
  padding-bottom: .35rem
}
.btn-group-lg>.btn,
.btn-lg {
  padding-top: .7rem;
  padding-bottom: .6rem
}
blockquote.icon:before {
  top: -.9rem
}
.counter-wrapper p {
  font-size: .85rem
}
.counter-wrapper .counter {
  font-size: calc(1.33rem + .96vw)
}
@media (min-width:1200px) {
  .counter-wrapper .counter {
    font-size: 2.05rem
  }
}
.counter-wrapper .counter.counter-lg {
  font-size: calc(1.35rem + 1.2vw)
}
@media (min-width:1200px) {
  .counter-wrapper .counter.counter-lg {
    font-size: 2.25rem
  }
}
.lead {
  font-size: .95rem;
  line-height: 1.6
}
.lead.fs-lg {
  line-height: 1.55
}
.display-1 {
  line-height: 1.15
}
.display-2 {
  line-height: 1.2
}
.display-3 {
  line-height: 1.2
}
.display-4 {
  line-height: 1.25
}
.display-5 {
  line-height: 1.25
}
.display-6 {
  line-height: 1.3
}