# ISOPUR Website — Augment Work‑Order

> **Purpose**
> Build a new marketing website for **IsoPur Kunststofftechnik GmbH** that
>
> 1. **Transplants the essential corporate & product information that appears on [https://www.isomat.com.de/](https://www.isomat.com.de/)** (IsoPur’s Private‑Label partner for Austria & Europe)
> 2. **Matches modern (≈2025) design expectations** with a *dark‑leaning, macOS‑style theme* (not pure black) and subtle motion/UX polish
> 3. **Removes all e‑commerce / shop functionality** – the site is purely informational & lead‑generating.
>
> The tasks below are for the **Agent Augment** extension running in VS Code. The workspace already contains a multi‑page HTML/CSS/JS template.

---

## 0  Context snapshot

| Source                       | Key data points                                                                                                                                                                                                                                                                                                                          |
| ---------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Isomat.com.de → Produkte** | 8 Hauptkategorien: *Bauwerksabdichtung, WDV‑Systeme, Farben & Oberflächenschutz, Fliesen‑ & Natursteinverlegung, Betonherstellung & ‑instandsetzung, Mauerwerksherstellung & ‑instandsetzung, Industriebeschichtungen, Dekorative Boden‑/Wandbeschichtung* ([isomat.com.de](https://www.isomat.com.de/produkte/?utm_source=chatgpt.com)) |
| **Unternehmen/Profil**       | Hersteller‑Gruppe (Gründung 1980), 3 Werke, 7 F\&E‑Labors, 350 + Produkte; DE‑Niederlassung Karl‑Wirth‑Str. 14, 76694 Forst ([isomat.com.de](https://www.isomat.com.de/uber_isomat/firmenprofil-isomat/?utm_source=chatgpt.com))                                                                                                         |
| **Kontakt DE**               | Tel. +49‑7251‑36915‑0, [<EMAIL>](mailto:<EMAIL>) ([isomat.com.de](https://www.isomat.com.de/kontakt/?utm_source=chatgpt.com))                                                                                                                                                                                      |
| **News**                     | Regelmäßige Messe‑ & Workshop‑Meldungen (z. B. „ISOMAT auf der BAU 2025“) ([isomat.com.de](https://www.isomat.com.de/news/?utm_source=chatgpt.com))                                                                                                                                                                                      |

> **Design cue (Competitor)**: Murexin.at uses muted greys + highlight yellow, full‑width hero, iconified category tiles ([murexin.at](https://www.murexin.at/?utm_source=chatgpt.com))
> Adopt the *information density & hierarchy*, *not* the color scheme.

---

## 1  High‑level deliverables

1. **Updated multi‑page static site** (HTML + CSS + JS) in the existing template folder.
2. **Lean IA (information architecture)**: see §3.
3. **Consistent visual language** (dark‑leaning, subtle glass‑morphism cards, motion on scroll).
4. **Content placeholders** (Markdown‑in‑HTML comments) where future CMS integration might inject real copy/links.

---

## 2  Design system guidelines

| Token                | Default value                                 | Notes                                             |
| -------------------- | --------------------------------------------- | ------------------------------------------------- |
| `--clr-bg`           | `#1a1a1c`                                     | overall canvas (≈ macOS dark)                     |
| `--clr-surface`      | `#252529`                                     | cards, nav, footer                                |
| `--clr-accent`       | `#ff4040`                                     | inherited from Isomat red; use sparingly for CTAs |
| `--clr-text-primary` | `#e6e6e6`                                     | body copy                                         |
| `--clr-text-muted`   | `#9a9a9a`                                     | meta, captions                                    |
| Font                 | **Inter Variable** via Google Fonts           | `font-display:swap`                               |
| Motion               | `scroll‑timeline` fade‑in & parallax hero img | prefers‑reduced‑motion respected                  |

> **Accessibility targets**: WCAG 2.2 AA (contrast ≥ 4.5), keyboard nav, ARIA‑landmarks.

---

## 3  Information architecture (keep)

```
/               → index.html      (hero + product‑category grid + USP stats + CTA)
/about/         → about.html      (company profile, mission, timeline, ISO/CE marks)
/solutions/     → solutions.html  (8 product categories, each anchors to §)
/projects/      → projects.html   (reference gallery slider + filter by category)
/downloads/     → downloads.html  (PDF tech sheets & EPD library links)
/news/          → news.html       (blog style list, optional if no content)
/contact/       → contact.html    (form + HQ addresses + map embed)
/impressum/     → impressum.html  (legal)
```

**Delete/ignore** any existing pages named `shop*.html`, `cart.html`, `checkout.html`, `my‑account.html`, etc.

---

## 4  Step‑by‑step task list for Augment

1. **Inventory workspace**

   ```bash
   find . -maxdepth 1 -name "*.html"
   ```

   Document current pages.
2. **Prune** unwanted shop/e‑commerce files; keep a *git branch* `archive/shop` just in case.
3. **Copy & scaffold** missing target pages from §3 (reuse template sections).
4. **Fetch remote content**
   *Scrape public pages from* `isomat.com.de` **once**; cache raw HTML in `/scrape/` folder.
   Parse:

   * company profile text (div `.content` in `/uber_isomat/firmenprofil-isomat/`)
   * list of 8 product categories (ul `.product-cats`)
   * recent news cards (first 3 in `/news/`)
5. **Populate** the new pages with **placeholder content**:

   * `<section id="categories">` → 8 grid tiles, icon + short excerpt.
   * `<section id="about">` timeline list.
   * Projects page: 6 dummy cards with picture from `/referenzen/` or Unsplash placeholders.
6. **Revise nav & footer**
   Add sticky header with *glass effect* (`backdrop-filter: blur(12px)`).
7. **Theme**: add `/styles/theme.css`, define CSS custom properties (§2); import Inter; override template colors.
8. **UI polish**:

   * `data-aos` fade‑up for modules.
   * `scroll-timeline` parallax hero (CSS only).
9. **Accessibility & performance** checks: Lighthouse ≥ 90/90/90/100.
10. **Commit** and push results.

---

## 5  Post‑development checklist

* [ ] All links & image paths are relative.
* [ ] No console errors in dev‑tools.
* [ ] 404 page present.
* [ ] Meta‑tags (`og:`, `twitter:`) filled with IsoPur data.
* [ ] Sitemap.xml & robots.txt updated.

---

## 6  Prompt for Agent Augment

```
You are Agent Augment working in a VS Code workspace that already contains a multi‑page static website template. Your mission:
1. Analyse all *.html files and list them.
2. Delete or archive any file related to shop/e‑commerce.
3. Ensure the following pages exist (create or refactor):
   index.html, about.html, solutions.html, projects.html, downloads.html, contact.html, impressum.html, [optional] news.html.
4. Crawl https://www.isomat.com.de once and extract:
   – The 8 main product categories and their titles.
   – Company profile paragraph.
   – The latest 3 news headlines + dates.
   Save raw HTML to /scrape for reproducibility.
5. Inject **placeholder** versions of the extracted content into the new pages (wrap dynamic text in <!-- TODO: ... --> comments so editors can replace later).
6. Apply a modern dark‑leaning theme:
   – Add /styles/theme.css with CSS variables as specified in README.
   – Update all pages to import theme.css after the main stylesheet.
7. Make all navigation links point to the new pages; footer likewise.
8. Implement subtle scroll animations using AOS (cdn) & CSS `scroll-timeline`.
9. Run Lighthouse; refactor until scores ≥ 90 performance, accessibility, best‑practices, SEO.
10. Commit your changes with descriptive messages.
```

---

*Last updated: 30 Jun 2025*
